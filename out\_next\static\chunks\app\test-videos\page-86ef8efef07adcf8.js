(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6950],{3631:(e,t,l)=>{"use strict";l.d(t,{CA:()=>i,No:()=>d,ZB:()=>u,iD:()=>h,tx:()=>m});let a={CURRENT_BATCH:"mytube_current_batch",BATCH_PREFIX:"mytube_batch_",VIDEO_INDEX:"mytube_video_index",TOTAL_VIDEOS:"mytube_total_videos",LAST_PROCESSED:"mytube_last_processed"};function o(e){for(let t of[/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,/youtube\.com\/v\/([^&\n?#]+)/]){let l=e.match(t);if(l)return l[1]}return null}function s(e){let t=o(e);return t?"https://www.youtube.com/embed/".concat(t):e}function r(e,t){return"Video ".concat(t+1)}function c(e){let t=function(e){try{let t=localStorage.getItem("".concat(a.BATCH_PREFIX).concat(e));if(!t)return null;let l=JSON.parse(t);if(Date.now()-l.lastUpdated>864e5)return localStorage.removeItem("".concat(a.BATCH_PREFIX).concat(e)),null;return l}catch(t){return console.error("Error loading batch ".concat(e,":"),t),null}}(e);return t?t.videos:[]}function n(){return c(parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"))}function i(){let e=(parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0")+1)%Math.ceil(parseInt(localStorage.getItem(a.TOTAL_VIDEOS)||"0")/100);return localStorage.setItem(a.CURRENT_BATCH,e.toString()),c(e)}function d(){let e=parseInt(localStorage.getItem(a.TOTAL_VIDEOS)||"0"),t=parseInt(localStorage.getItem(a.CURRENT_BATCH)||"0"),l=Math.ceil(e/100),o=c(t);return{totalVideos:e,currentBatch:t,totalBatches:l,videosInCurrentBatch:o.length}}function h(){Object.keys(localStorage).forEach(e=>{(e.startsWith(a.BATCH_PREFIX)||Object.values(a).includes(e))&&localStorage.removeItem(e)}),console.log("Cleared all video storage")}async function m(){try{let e=await fetch("/Mytube.json");if(!e.ok)throw Error("Failed to load videos: ".concat(e.statusText));let t=await e.json();console.log("Raw video data loaded:",Object.keys(t).length,"entries");let l=[];return Array.isArray(t)?t.forEach((e,t)=>{Object.entries(e).forEach(e=>{let[t,a]=e,c=o(a);c&&l.push({id:"video_".concat(l.length,"_").concat(c),title:r(a,l.length),url:a,embedUrl:s(a),duration:300,category:"General",batchIndex:Math.floor(l.length/100)})})}):Object.entries(t).forEach((e,t)=>{let[a,c]=e,n=o(c);n&&l.push({id:"video_".concat(l.length,"_").concat(n),title:r(c,l.length),url:c,embedUrl:s(c),duration:300,category:"General",batchIndex:Math.floor(l.length/100)})}),l}catch(e){throw console.error("Error loading videos from file:",e),e}}async function u(){try{if(!function(){let e=localStorage.getItem(a.LAST_PROCESSED);return!e||Date.now()-parseInt(e)>864e5}())return console.log("Using cached video data..."),n();{console.log("Loading fresh video data...");let e=await m();return!function(e){let t=Math.ceil(e.length/100);for(let o=0;o<t;o++){let t=100*o,s=Math.min(t+100,e.length),r=e.slice(t,s);var l=o;try{let e={batchNumber:l,videos:r,totalVideos:r.length,lastUpdated:Date.now()};localStorage.setItem("".concat(a.BATCH_PREFIX).concat(l),JSON.stringify(e))}catch(e){console.error("Error saving batch ".concat(l,":"),e)}}localStorage.setItem(a.TOTAL_VIDEOS,e.length.toString()),localStorage.setItem(a.CURRENT_BATCH,"0"),localStorage.setItem(a.LAST_PROCESSED,Date.now().toString()),console.log("Saved ".concat(e.length," videos in ").concat(t," batches"))}(e),n()}}catch(t){console.error("Error initializing video system:",t);let e=n();if(e.length>0)return console.log("Using cached videos as fallback"),e;throw t}}},7420:(e,t,l)=>{Promise.resolve().then(l.bind(l,9072))},9072:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>r});var a=l(5155),o=l(2115),s=l(3631);function r(){let[e,t]=(0,o.useState)([]),[l,r]=(0,o.useState)({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),[c,n]=(0,o.useState)(!1),[i,d]=(0,o.useState)(null),h=async()=>{n(!0),d(null);try{console.log("Loading videos from file...");let e=await (0,s.tx)();console.log("Videos loaded:",e.length),t(e.slice(0,10));let l=(0,s.No)();r(l)}catch(e){console.error("Error loading videos:",e),d(e.message)}finally{n(!1)}},m=async()=>{n(!0),d(null);try{console.log("Initializing video system...");let e=await (0,s.ZB)();console.log("System initialized, current batch:",e.length),t(e.slice(0,10));let l=(0,s.No)();r(l)}catch(e){console.error("Error initializing system:",e),d(e.message)}finally{n(!1)}};return(0,a.jsx)("div",{className:"min-h-screen p-4",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-white mb-6",children:"Video System Test"}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Test Controls"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)("button",{onClick:h,disabled:c,className:"btn-primary",children:c?"Loading...":"Load Videos from File"}),(0,a.jsx)("button",{onClick:m,disabled:c,className:"btn-secondary",children:c?"Loading...":"Initialize Video System"}),(0,a.jsx)("button",{onClick:()=>{(0,s.iD)(),t([]),r({totalVideos:0,currentBatch:0,totalBatches:0,videosInCurrentBatch:0}),console.log("Storage cleared")},className:"btn-danger",children:"Clear Storage"})]})]}),(0,a.jsxs)("div",{className:"glass-card p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Video Statistics"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:l.totalVideos}),(0,a.jsx)("p",{className:"text-white/60",children:"Total Videos"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold text-blue-400",children:[l.currentBatch+1,"/",l.totalBatches]}),(0,a.jsx)("p",{className:"text-white/60",children:"Current Batch"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-400",children:l.videosInCurrentBatch}),(0,a.jsx)("p",{className:"text-white/60",children:"Videos in Batch"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:e.length}),(0,a.jsx)("p",{className:"text-white/60",children:"Displayed"})]})]})]}),i&&(0,a.jsxs)("div",{className:"glass-card p-6 mb-6 border-red-500 border",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-red-400 mb-2",children:"Error"}),(0,a.jsx)("p",{className:"text-white",children:i})]}),(0,a.jsxs)("div",{className:"glass-card p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-white mb-4",children:"Videos Preview (First 10)"}),0===e.length?(0,a.jsx)("p",{className:"text-white/60 text-center py-8",children:"No videos loaded. Click a button above to test."}):(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white/10 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"aspect-video mb-3",children:(0,a.jsx)("iframe",{src:e.embedUrl,title:e.title,className:"w-full h-full rounded",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0})}),(0,a.jsx)("h3",{className:"text-white font-medium text-sm mb-2",children:e.title}),(0,a.jsxs)("div",{className:"text-xs text-white/60 space-y-1",children:[(0,a.jsxs)("p",{children:["ID: ",e.id]}),(0,a.jsxs)("p",{children:["Duration: ",e.duration,"s"]}),(0,a.jsxs)("p",{children:["Batch: ",e.batchIndex]})]})]},e.id))})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(7420)),_N_E=e.O()}]);