(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8039],{152:(e,s,t)=>{Promise.resolve().then(t.bind(t,1901))},1469:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return c},getImageProps:function(){return n}});let a=t(8229),r=t(8883),l=t(3063),i=a._(t(1193));function n(e){let{props:s}=(0,r.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,t]of Object.entries(s))void 0===t&&delete s[e];return{props:s}}let c=l.Image},1901:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),n=t(6766);function c(e){let{error:s,reset:t}=e;return(0,r.useEffect)(()=>{console.error("Application error:",s)},[s]),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)(n.default,{src:"/img/mytube-logo.svg",alt:"MyTube Logo",width:80,height:80,className:"mx-auto mb-4"}),(0,a.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"Oops!"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Something went wrong"}),(0,a.jsx)("p",{className:"text-white/80 mb-8 max-w-md mx-auto",children:"We encountered an unexpected error. Please try again or contact support if the problem persists."}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("p",{className:"text-white/60 mb-4",children:"Need immediate help?"}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"fas fa-envelope mr-2"}),"Email Support"]})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("button",{onClick:t,className:"btn-primary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-redo mr-2"}),"Try Again"]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(i(),{href:"/",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-home mr-2"}),"Go Home"]}),(0,a.jsxs)(i(),{href:"/dashboard",className:"btn-secondary inline-flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-tachometer-alt mr-2"}),"Dashboard"]})]})]}),!1]})})}},6766:(e,s,t)=>{"use strict";t.d(s,{default:()=>r.a});var a=t(1469),r=t.n(a)}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,3063,8441,1684,7358],()=>s(152)),_N_E=e.O()}]);